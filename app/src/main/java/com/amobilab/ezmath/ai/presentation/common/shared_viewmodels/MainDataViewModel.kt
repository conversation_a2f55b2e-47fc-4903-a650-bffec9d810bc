package com.amobilab.ezmath.ai.presentation.common.shared_viewmodels

import amobi.module.common.CommApplication
import amobi.module.common.configs.PrefAssist
import amobi.module.common.configs.RconfAssist
import amobi.module.common.utils.FirebaseAssist
import amobi.module.common.utils.debugLog
import amobi.module.common.utils.debugLogTrace
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.RectF
import android.os.Bundle
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.asAndroidBitmap
import androidx.core.net.toUri
import androidx.lifecycle.viewModelScope
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.app.BaseViewModel
import com.amobilab.ezmath.ai.data.db.AppDatabase
import com.amobilab.ezmath.ai.data.db.powerSync.AppSchema
import com.amobilab.ezmath.ai.data.db.powerSync.ChatEntity
import com.amobilab.ezmath.ai.data.db.powerSync.HistoryEntity
import com.amobilab.ezmath.ai.data.db.powerSync.HistoryRepository
import com.amobilab.ezmath.ai.data.models.Chat
import com.amobilab.ezmath.ai.data.models.ChatState
import com.amobilab.ezmath.ai.data.models.ChatUiEvent
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.data.pref.RconfConst
import com.amobilab.ezmath.ai.presentation.common.shared_values.BotType
import com.amobilab.ezmath.ai.presentation.common.shared_values.ChatQuestionMode
import com.amobilab.ezmath.ai.presentation.common.shared_values.ModelAiMode
import com.amobilab.ezmath.ai.utils.BitmapUtils
import com.amobilab.ezmath.ai.values.Const
import com.powersync.DatabaseDriverFactory
import com.powersync.PowerSyncDatabase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.joda.time.LocalTime
import java.io.File
import java.io.FileOutputStream
import java.util.Base64
import java.util.UUID
import javax.inject.Inject
import kotlin.math.roundToLong

@HiltViewModel
class MainDataViewModel @Inject constructor() : BaseViewModel() {

    val modeFromScan = mutableStateOf(ChatQuestionMode.Math)

    var isLoadingData by mutableStateOf(false)
        private set

    fun getBitmapFromFile(filePath: String): Bitmap? {
        val file = File(filePath)
        return if (file.exists()) {
            BitmapFactory.decodeFile(file.absolutePath)
        } else {
            null
        }
    }

    fun saveImageToCache(context: Context, bitmap: ImageBitmap): String {
        // Lưu Bitmap vào một file ảnh tạm thời
        val tempFile = File.createTempFile("temp_image", ".jpg", context.cacheDir)
        bitmap
            .asAndroidBitmap()
            .compress(Bitmap.CompressFormat.JPEG, 100, FileOutputStream(tempFile))

        return tempFile.absolutePath
    }


    var frameRect by mutableStateOf<RectF?>(null)
        private set

    fun saveFrameRect(newFrameRect: RectF) {
        frameRect = newFrameRect
    }

    // ===========================================================================================================================

    fun saveSortBy(sortBy: String) {
        PrefAssist.setString(PrefConst.SORT_BY, sortBy)
    }

    // ===========================================================================================================================
    // AiChat
    //============================

    var insertHistory: MutableState<Boolean> = mutableStateOf(true)
        private set

    var modeScan: MutableState<ChatQuestionMode> = mutableStateOf(ChatQuestionMode.Math)
        private set

    private var idHistory = ""

    private val _chatState = MutableStateFlow(ChatState())
    val chatState = _chatState.asStateFlow()


    fun loadChatsForHistory(idHistory: String) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                <EMAIL> = idHistory
                val historyList =
                    AppDatabase.Companion.getInstance().getChatRepository()
                        .getChatsForHistory(idHistory)

                debugLog("aab historyList ${historyList[0].imageData}")
                val chatList = historyList.reversed().map { historyEntity ->
                    Chat(
                        idChat = historyEntity.id,
                        prompt = historyEntity.content,
                        bitmap = historyEntity.imageData?.let {
                            BitmapFactory.decodeByteArray(
                                it,
                                0,
                                it.size
                            )
                        },
                        isFromUser = historyEntity.isHuman,
                        isError = historyEntity.isError,
                        botType = when (historyEntity.botName) {
                            Const.AiServiceName.GPT -> BotType.BOT_GPT
                            Const.AiServiceName.GEMINI -> BotType.BOT_GEMINI
                            else -> null
                        }
                    )
                }.toMutableList()

                _chatState.value = ChatState(chatList = chatList)

            } catch (e: Exception) {
                debugLog("aab lỗi $e")
                _chatState.value = ChatState()
            }
        }
    }

    val isCountCoin = mutableStateOf(false)

    // Biến lưu trữ prompt cuối cùng khi không đủ coin
    private val _lastPromptToSend = MutableStateFlow("")
    val lastPromptToSend = _lastPromptToSend.asStateFlow()
    fun onEvent(context: Context, event: ChatUiEvent) {
        when (event) {
            is ChatUiEvent.SendPrompt -> {
                val promptToSend = event.prompt

                if (promptToSend.isNotEmpty() || event.bitmap != null) {

                    // tắt trừ coin để test
                    if (PrefAssist.getInt(PrefConst.FREE_CHAT) <= 0) {
                        isCountCoin.value = true
                        if (PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE) <= 0) {
//                            MixedUtils.showToast(context, R.string.txtid_not_enough_coin)
                            // Lưu promptToSend khi không đủ coin
                            _lastPromptToSend.value = promptToSend
                            coinViewModel.showInsufficientCoinDialog()
                            return
                        }
                    } else {
                        isCountCoin.value = false

                        coinViewModel.countdownFreeChat()
                    }

                    val timestamp = System.currentTimeMillis()

                    val uid = UUID.randomUUID().toString()

                    isLoadingData = true
                    // Nếu có bitmap, gọi hàm xử lý với hình ảnh
                    if (event.bitmap != null) {
                        _chatState.update {
                            it.copy(
                                chatList = it.chatList.toMutableList().apply {
                                    add(
                                        0,
                                        Chat(
                                            uid,
                                            promptToSend,
                                            event.bitmap,
                                            true,
                                            false,
                                            null
                                        )
                                    )
                                },
                                prompt = "",
                                bitmap = null
                            )
                        }
                        if (RconfAssist.getBoolean(RconfConst.IS_USE_GATEWAY_API)) {
                            // Chọn model dựa trên giá trị trong SharedPreferences
                            if (PrefAssist.getString(PrefConst.MODEL_AI) != ModelAiMode.GEMINI.name) {
                                getResponseWithImageAIGateway(context, promptToSend, event.bitmap)
                            } else {
                                getResponseWithImageAIGateway(context, promptToSend, event.bitmap)

                            }
                        }else{
                            if (PrefAssist.getString(PrefConst.MODEL_AI) != ModelAiMode.GEMINI.name) {
                                getResponseWithImageGPT(context, promptToSend, event.bitmap)
                            } else {
                                getResponseWithImageGemini(context, promptToSend, event.bitmap)
                            }
                        }
                    } else {
                        val uid = UUID.randomUUID().toString()
                        _chatState.update {
                            it.copy(
                                chatList = it.chatList.toMutableList().apply {
                                    add(
                                        0,
                                        Chat(
                                            uid,
                                            promptToSend,
                                            null,
                                            true,
                                            false,
                                            null
                                        )
                                    )
                                },
                                prompt = "",
                                bitmap = null
                            )
                        }


                        val reverseListChat = chatState.value.chatList


                        // Chọn model dựa trên giá trị trong SharedPreferences
                        if (RconfAssist.getBoolean(RconfConst.IS_USE_GATEWAY_API))
                            getResponsesAIGateway(context, reverseListChat, promptToSend)
                        else
                            if (PrefAssist.getString(PrefConst.MODEL_AI) != ModelAiMode.GEMINI.name) {
//                                reverseListChat.add(
//                                    0,
//                                    Chat(
//                                        idChat = timestamp,
//                                        prompt = promptToSend,
//                                        bitmap = null,
//                                        isFromUser = true,
//                                        isError = false,
//                                        botType = BotType.BOT_GPT
//                                    )
//                                )
                                getResponsesGpt(context, reverseListChat, promptToSend)
                            } else {
                                getResponsesGemini(context, reverseListChat, promptToSend)
                            }
                    }
                }
            }

            is ChatUiEvent.UpdatePrompt -> {
                _chatState.update {
                    it.copy(prompt = event.newPrompt)
                }
            }

            is ChatUiEvent.ResendMessage -> {
//                if (PrefAssist.getInt(PrefConst.FREE_CHAT) <= 0) {
//                    isCountCoin.value = true
//                    if (PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE) <= 0) {
////                            MixedUtils.showToast(context, R.string.txtid_not_enough_coin)
//                        // Lưu prompt của tin nhắn cần gửi lại khi không đủ coin
//                        _lastPromptToSend.value = event.prompt
//                        coinViewModel.showInsufficientCoinDialog()
//                        return
//                    }
//                } else {
//                    isCountCoin.value = false
//
//                    coinViewModel.countdownFreeChat()
//                }

                val listChat = chatState.value.chatList

                isLoadingData = true


                val lastErrorMessageIndex = listChat.indexOfLast { it.isError }
                debugLog("lastErrorMessageIndex: $lastErrorMessageIndex")
                debugLog("lastErrorMessageIndexSize: ${listChat.size}")
                if (lastErrorMessageIndex != -1) {
                    val lastErrorMessage = listChat[lastErrorMessageIndex + 1]

                    _chatState.update {
                        it.copy(
                            chatList = it.chatList.toMutableList().apply {
                                removeAt(lastErrorMessageIndex)
                            }
                        )
                    }
                    // Nếu có bitmap, gọi hàm xử lý với hình ảnh
                    if (lastErrorMessage.bitmap != null) {
                        // Chọn model dựa trên giá trị trong SharedPreferences
                        if (RconfAssist.getBoolean(RconfConst.IS_USE_GATEWAY_API))
                            getResponseWithImageAIGateway(
                                context,
                                lastErrorMessage.prompt,
                                lastErrorMessage.bitmap,
                                true
                            )
                        else
                            if (PrefAssist.getString(PrefConst.MODEL_AI) != ModelAiMode.GEMINI.name) {
                                getResponseWithImageGPT(
                                    context,
                                    lastErrorMessage.prompt,
                                    lastErrorMessage.bitmap,
                                    true
                                )
                            } else {
                                getResponseWithImageGemini(
                                    context,
                                    lastErrorMessage.prompt,
                                    lastErrorMessage.bitmap,
                                    true
                                )

                            }
                    } else {
                        // Chọn model dựa trên giá trị trong SharedPreferences
                        if (RconfAssist.getBoolean(RconfConst.IS_USE_GATEWAY_API))
                            getResponsesAIGateway(context, listChat, lastErrorMessage.prompt, true)
                        else
                            if (PrefAssist.getString(PrefConst.MODEL_AI) != ModelAiMode.GEMINI.name) {
                                getResponsesGpt(context, listChat, lastErrorMessage.prompt, true)
                            } else {
                                getResponsesGemini(context, listChat, lastErrorMessage.prompt, true)
                            }
                    }
                }
            }


        }
    }

    private var shouldStopCollecting = false
    private fun getResponsesGpt(
        context: Context,
        listChat: MutableList<Chat>,
        prompt: String,
        isResendMessage: Boolean? = false
    ) {
        FirebaseAssist.Companion.instance.logCustomEvent(
            "query_ai_response",
            Bundle().apply {
                putString("modal_type", "text")
                putString("question_mode", modeFromScan.value.id.lowercase())
                putString("ai_type", "chatgpt")
                putString("local_hour", LocalTime.now().hourOfDay.toString() + "h")
            }
        )
        viewModelScope.launch {
            shouldStopCollecting = false
            val timestamp = System.currentTimeMillis()
            if (insertHistory.value) {
                if (idHistory == "") {
                    idHistory = AppDatabase.Companion.getInstance().getHistoryRepository()
                        .insertHistory(
                            history = HistoryEntity(
                                timestamp = timestamp,
                                historyName = "History $timestamp",
                                isFavorite = false,
                                content = prompt,
                                imageData = null,
                                questionMode = modeFromScan.value.id,
                                modelAiChat = PrefAssist.getString(PrefConst.MODEL_AI)
                            )
                        )
                    updateHistoryTitle(prompt, null, idHistory)
                }
                insertHistory.value = false
            }

            if (isResendMessage == false) {
                saveChatData(idHistory, timestamp, prompt, true, null, Const.AiServiceName.GPT)
            }

            val uid = UUID.randomUUID().toString()
            var chats = Chat(
                idChat = uid,
                prompt = "",
                bitmap = null,
                isFromUser = false,
                isError = false,
                botType = BotType.BOT_GPT
            )
            var prompts = ""

            _chatState.update {
                it.copy(
                    chatList = it.chatList.toMutableList().apply {
                        add(0, chats)
                    }
                )
            }
            var inputTokenCount = 0
            var outputTokenCount = 0


            gptApi.getResponses(context, listChat, modeScan.value)
                .onCompletion {
                    isLoadingData = false

                    val coinsCount: Double =
                        inputTokenCount.toDouble() * (RconfAssist.getLong(RconfConst.TOKEN_PROMPT_TO_COIN_GPT) / 1000.0) +
                                outputTokenCount.toDouble() * (RconfAssist.getLong(RconfConst.TOKEN_PROMPT_TO_COIN_GPT) / 1000.0)

                    if (isCountCoin.value) {
                        coinViewModel.updateCoinBalance(
                            -coinsCount.roundToLong(),
                            CommApplication.Companion.appContext.getString(R.string.query_text_with_gpt)
                        )
                    }

                    if (isResendMessage == true) {
                        updateChatData(
                            idHistory,
                            timestamp,
                            chats.prompt,
                            false,
                            null,
                            Const.AiServiceName.GPT
                        )
                    } else {
                        saveChatData(
                            idHistory,
                            timestamp,
                            chats.prompt,
                            false,
                            null,
                            Const.AiServiceName.GPT
                        )
                    }
                }
                .collect { chatResponse ->
                    if (shouldStopCollecting) {
                        gptApi.stopCollecting()
                        return@collect
                    }
                    if (chatResponse != null) {
                        // Thêm từng từ vào prompts
                        val newPrompts = chatResponse.chat.prompt.split("") // Tách từng từ
                        newPrompts.forEach { word ->
                            prompts += word // Cập nhật prompts với từng từ
                            inputTokenCount = chatResponse.inputTokenCount
                            outputTokenCount = chatResponse.outputTokenCount

                            // Cập nhật chats với prompts mới
                            chats = chats.copy(
                                prompt = prompts,
                                isError = chatResponse.chat.isError,
                            )

                            // Cập nhật chatState
                            _chatState.update {
                                it.copy(
                                    chatList = it.chatList.toMutableList().apply {
                                        if (isNotEmpty()) set(0, chats)
                                    }
                                )
                            }
                            delay(10)
                        }
                    }
                }
        }
    }

    private fun getResponsesGemini(
        context: Context,
        listChat: MutableList<Chat>,
        prompt: String,
        isResendMessage: Boolean? = false
    ) {
        FirebaseAssist.Companion.instance.logCustomEvent(
            "query_ai_response",
            Bundle().apply {
                putString("modal_type", "text")
                putString("question_mode", modeFromScan.value.id.lowercase())
                putString("ai_type", "gemini")
                putString("local_hour", LocalTime.now().hourOfDay.toString() + "h")
            }
        )
        viewModelScope.launch {
            shouldStopCollecting = false
            val timestamp = System.currentTimeMillis()
            if (insertHistory.value) {
                if (idHistory == "") {
                    idHistory = AppDatabase.Companion.getInstance().getHistoryRepository()
                        .insertHistory(
                            history = HistoryEntity(
                                timestamp = timestamp,
                                historyName = "History $timestamp",
                                isFavorite = false,
                                content = prompt,
                                imageData = null,
                                questionMode = modeFromScan.value.id,
                                modelAiChat = PrefAssist.getString(PrefConst.MODEL_AI)
                            )
                        )
                    updateHistoryTitle(prompt, null, idHistory)
                }
                insertHistory.value = false
            }

            if (isResendMessage == false) {
                saveChatData(idHistory, timestamp, prompt, true, null, Const.AiServiceName.GEMINI)
            }

            val uid = UUID.randomUUID().toString()
            var chats = Chat(
                idChat = uid,
                prompt = "",
                bitmap = null,
                isFromUser = false,
                isError = false,
                botType = BotType.BOT_GEMINI
            )
            var prompts = ""

            _chatState.update {
                it.copy(
                    chatList = it.chatList.toMutableList().apply {
                        add(0, chats)
                    }
                )
            }
            var inputTokenCount = 0
            var outputTokenCount = 0

            geminiApi.getResponses(context, listChat, prompt, modeScan.value)
                .onCompletion {
                    isLoadingData = false


                    val coinsCount: Double =
                        inputTokenCount.toDouble() * (RconfAssist.getLong(RconfConst.TOKEN_PROMPT_TO_COIN_GEMINI) / 1000.0) +
                                outputTokenCount.toDouble() * (RconfAssist.getLong(RconfConst.TOKEN_ANSWER_TO_COIN_GEMINI) / 1000.0)

                    if (isCountCoin.value) {
                        coinViewModel.updateCoinBalance(
                            -coinsCount.roundToLong(),
                            CommApplication.Companion.appContext.getString(R.string.query_text_with_gemini)
                        )
                    }

                    if (isResendMessage == true) {
                        updateChatData(
                            idHistory,
                            timestamp,
                            chats.prompt,
                            false,
                            null,
                            Const.AiServiceName.GEMINI
                        )
                    } else {
                        saveChatData(
                            idHistory,
                            timestamp,
                            chats.prompt,
                            false,
                            null,
                            Const.AiServiceName.GEMINI
                        )
                    }

                }
                .collect { chatResponse ->
                    if (shouldStopCollecting) {
                        geminiApi.stopCollecting()
                        return@collect
                    }
                    if (chatResponse != null) {
                        // Thêm từng từ vào prompts
                        val newPrompts = chatResponse.chat.prompt.split("") // Tách từng từ
                        newPrompts.forEach { word ->
                            prompts += word // Cập nhật prompts với từng từ
                            inputTokenCount = chatResponse.inputTokenCount
                            outputTokenCount = chatResponse.outputTokenCount

                            // Cập nhật chats với prompts mới
                            chats = chats.copy(
                                prompt = prompts,
                                isError = chatResponse.chat.isError,
                            )

                            // Cập nhật chatState
                            _chatState.update {
                                it.copy(
                                    chatList = it.chatList.toMutableList().apply {
                                        if (isNotEmpty()) set(0, chats)
                                    }
                                )
                            }
                            delay(10)
                        }
                    }
                }
        }
    }

    fun stopListening() {
        shouldStopCollecting = true
    }

    private fun getResponseWithImageGPT(
        context: Context,
        prompt: String,
        bitmap: Bitmap,
        isResendMessage: Boolean? = false
    ) {
        FirebaseAssist.Companion.instance.logCustomEvent(
            "query_ai_response",
            Bundle().apply {
                putString("modal_type", "image")
                putString("question_mode", modeFromScan.value.id.lowercase())
                putString("ai_type", "chatgpt")
                putString("local_hour", LocalTime.now().hourOfDay.toString() + "h")
            }
        )
        viewModelScope.launch {
            val timestamp = System.currentTimeMillis()
            val byteArrayBitmap = BitmapUtils.getByteArrayFromBitmap(bitmap)
            if (insertHistory.value) {
                if (idHistory == "") {
                    idHistory = AppDatabase.Companion.getInstance().getHistoryRepository()
                        .insertHistory(
                            history = HistoryEntity(
                                timestamp = timestamp,
                                historyName = "History $timestamp",
                                isFavorite = false,
                                content = prompt,
                                imageData = byteArrayBitmap,
                                questionMode = modeFromScan.value.id,
                                modelAiChat = PrefAssist.getString(PrefConst.MODEL_AI)
                            )
                        )
                    updateHistoryTitle(prompt, bitmap, idHistory)
                }
                insertHistory.value = false
            }

            if (isResendMessage == false) {
                saveChatData(
                    idHistory,
                    timestamp,
                    prompt,
                    true,
                    byteArrayBitmap,
                    Const.AiServiceName.GPT
                )
            }

            val uid = UUID.randomUUID().toString()

            var chats = Chat(
                idChat = uid,
                prompt = "",
                bitmap = bitmap,
                isFromUser = false,
                isError = false,
                botType = BotType.BOT_GPT
            )
            var prompts = ""

            _chatState.update {
                it.copy(
                    chatList = it.chatList.toMutableList().apply {
                        add(0, chats)
                    }
                )
            }

            var inputTokenCount = 0
            var outputTokenCount = 0
            gptApi.getResponseWithImage(context, prompt, bitmap, modeScan.value)
                .onCompletion {
                    val coinsCount: Double =
                        inputTokenCount.toDouble() * (RconfAssist.getLong(RconfConst.TOKEN_PROMPT_TO_COIN_GPT) / 1000.0) +
                                outputTokenCount.toDouble() * (RconfAssist.getLong(RconfConst.TOKEN_ANSWER_TO_COIN_GPT) / 1000.0)

                    if (isCountCoin.value) {
                        coinViewModel.updateCoinBalance(
                            -coinsCount.roundToLong(),
                            CommApplication.Companion.appContext.getString(R.string.query_image_with_gemini)
                        )
                    }

                    if (isResendMessage == true) {
                        updateChatData(
                            idHistory,
                            timestamp,
                            chats.prompt,
                            false,
                            byteArrayBitmap,
                            Const.AiServiceName.GPT
                        )
                    } else {
                        saveChatData(
                            idHistory,
                            timestamp,
                            chats.prompt,
                            false,
                            byteArrayBitmap,
                            Const.AiServiceName.GPT
                        )
                    }
                    isLoadingData = false

                }
                .collect { chatResponse ->
                    if (shouldStopCollecting) {
                        gptApi.stopCollecting()
                        return@collect
                    }
                    // Thêm từng từ vào prompts
                    val newPrompts = chatResponse.chat.prompt.split("") // Tách từng từ
                    newPrompts.forEach { word ->
                        prompts += word // Cập nhật prompts với từng từ
                        inputTokenCount = chatResponse.inputTokenCount
                        outputTokenCount = chatResponse.outputTokenCount

                        // Cập nhật chats với prompts mới
                        chats = chats.copy(
                            prompt = prompts,
                            isError = chatResponse.chat.isError,
                        )

                        // Cập nhật chatState
                        _chatState.update {
                            it.copy(
                                chatList = it.chatList.toMutableList().apply {
                                    if (isNotEmpty()) set(0, chats)
                                }
                            )
                        }
                        delay(10)
                    }
                }
        }
    }

    private fun getResponseWithImageGemini(
        context: Context,
        prompt: String,
        bitmap: Bitmap,
        isResendMessage: Boolean? = false
    ) {
        FirebaseAssist.Companion.instance.logCustomEvent(
            "query_ai_response",
            Bundle().apply {
                putString("modal_type", "image")
                putString("question_mode", modeFromScan.value.id.lowercase())
                putString("ai_type", "gemini")
                putString("local_hour", LocalTime.now().hourOfDay.toString() + "h")
            }
        )
        viewModelScope.launch {
            val timestamp = System.currentTimeMillis()
            val byteArrayBitmap = BitmapUtils.getByteArrayFromBitmap(bitmap)
            if (insertHistory.value) {
                if (idHistory == "") {
                    idHistory = AppDatabase.Companion.getInstance().getHistoryRepository()
                        .insertHistory(
                            history = HistoryEntity(
                                timestamp = timestamp,
                                historyName = "History $timestamp",
                                isFavorite = false,
                                content = prompt,
                                imageData = byteArrayBitmap,
                                questionMode = modeFromScan.value.id,
                                modelAiChat = PrefAssist.getString(PrefConst.MODEL_AI)
                            )
                        )
                    updateHistoryTitle(prompt, bitmap, idHistory)
                }
                insertHistory.value = false
            }

            if (isResendMessage == false) {
                saveChatData(
                    idHistory,
                    timestamp,
                    prompt,
                    true,
                    byteArrayBitmap,
                    Const.AiServiceName.GEMINI
                )
            }

            val uid = UUID.randomUUID().toString()

            var chats = Chat(
                idChat = uid,
                prompt = "",
                bitmap = bitmap,
                isFromUser = false,
                isError = false,
                botType = BotType.BOT_GEMINI
            )
            var prompts = ""

            _chatState.update {
                it.copy(
                    chatList = it.chatList.toMutableList().apply {
                        add(0, chats)
                    }
                )
            }

            var inputTokenCount = 0
            var outputTokenCount = 0
            geminiApi.getResponseWithImage(context, prompt, bitmap, modeScan.value)
                .onCompletion {
                    val coinsCount: Double =
                        inputTokenCount.toDouble() * (RconfAssist.getLong(RconfConst.TOKEN_PROMPT_TO_COIN_GEMINI) / 1000.0) +
                                outputTokenCount.toDouble() * (RconfAssist.getLong(RconfConst.TOKEN_ANSWER_TO_COIN_GEMINI) / 1000.0)

                    if (isCountCoin.value) {
                        coinViewModel.updateCoinBalance(
                            -coinsCount.roundToLong(),
                            CommApplication.Companion.appContext.getString(R.string.query_image_with_gpt)
                        )
                    }

                    if (isResendMessage == true) {
                        updateChatData(
                            idHistory,
                            timestamp,
                            chats.prompt,
                            false,
                            byteArrayBitmap,
                            Const.AiServiceName.GEMINI
                        )
                    } else {
                        saveChatData(
                            idHistory,
                            timestamp,
                            chats.prompt,
                            false,
                            byteArrayBitmap,
                            Const.AiServiceName.GEMINI
                        )
                    }
                    isLoadingData = false

                }
                .collect { chatResponse ->
                    if (shouldStopCollecting) {
                        geminiApi.stopCollecting()
                        return@collect // Exit the collect loop if the flag is set
                    }
                    if (chatResponse != null) {
                        // Thêm từng từ vào prompts
                        val newPrompts = chatResponse.chat.prompt.split("") // Tách từng chữ
                        newPrompts.forEach { word ->
                            prompts += word // Cập nhật prompts với từng chữ
                            inputTokenCount = chatResponse.inputTokenCount
                            outputTokenCount = chatResponse.outputTokenCount

                            // Cập nhật chats với prompts mới
                            chats = chats.copy(
                                prompt = prompts,
                                isError = chatResponse.chat.isError,
                            )

                            // Cập nhật chatState
                            _chatState.update {
                                it.copy(
                                    chatList = it.chatList.toMutableList().apply {
                                        if (isNotEmpty()) set(0, chats)
                                    }
                                )
                            }

                            // Delay để thấy rõ sự thay đổi
                            delay(10) // 50 ms
                        }
                    }
                }
        }
    }


    private fun getResponsesAIGateway(
        context: Context,
        listChat: MutableList<Chat>,
        prompt: String,
        isResendMessage: Boolean? = false
    ) {
        val botName =
            if (PrefAssist.getString(PrefConst.MODEL_AI) == ModelAiMode.GPT.name) Const.AiServiceName.GPT
            else Const.AiServiceName.GEMINI
        val botType =
            if (PrefAssist.getString(PrefConst.MODEL_AI) == ModelAiMode.GPT.name) BotType.BOT_GPT
            else BotType.BOT_GEMINI

        FirebaseAssist.Companion.instance.logCustomEvent(
            "query_ai_response",
            Bundle().apply {
                putString("modal_type", "text")
                putString("question_mode", modeFromScan.value.id.lowercase())
                putString("ai_type", botName)
                putString("local_hour", LocalTime.now().hourOfDay.toString() + "h")
            }
        )
        viewModelScope.launch {
            shouldStopCollecting = false
            val timestamp = System.currentTimeMillis()
            if (insertHistory.value) {
                if (idHistory == "") {
                    idHistory = AppDatabase.Companion.getInstance().getHistoryRepository()
                        .insertHistory(
                            history = HistoryEntity(
                                timestamp = timestamp,
                                historyName = "History $timestamp",
                                isFavorite = false,
                                content = prompt,
                                imageData = null,
                                questionMode = modeFromScan.value.id,
                                modelAiChat = PrefAssist.getString(PrefConst.MODEL_AI)
                            )
                        )
                    updateHistoryTitle(prompt, null, idHistory)
                }
                insertHistory.value = false
            }

            if (isResendMessage == false) {
                saveChatData(idHistory, timestamp, prompt, true, null, Const.AiServiceName.GPT)
            }

            val uid = UUID.randomUUID().toString()

            var chats = Chat(
                idChat = uid,
                prompt = "",
                bitmap = null,
                isFromUser = false,
                isError = false,
                botType = botType
            )
            var prompts = ""

            _chatState.update {
                it.copy(
                    chatList = it.chatList.toMutableList().apply {
                        add(0, chats)
                    }
                )
            }
            var inputTokenCount = 0
            var outputTokenCount = 0


            gatewayApi.getResponses(context, listChat, modeScan.value)
                .onCompletion {
                    isLoadingData = false

                    val coinsCount: Double = if (PrefAssist.getString(PrefConst.MODEL_AI) != ModelAiMode.GEMINI.name) {
                        inputTokenCount.toDouble() * (RconfAssist.getLong(RconfConst.TOKEN_PROMPT_TO_COIN_GPT) / 1000.0) +
                                outputTokenCount.toDouble() * (RconfAssist.getLong(RconfConst.TOKEN_PROMPT_TO_COIN_GPT) / 1000.0)
                    } else {
                        inputTokenCount.toDouble() * (RconfAssist.getLong(RconfConst.TOKEN_PROMPT_TO_COIN_GEMINI) / 1000.0) +
                                outputTokenCount.toDouble() * (RconfAssist.getLong(RconfConst.TOKEN_ANSWER_TO_COIN_GEMINI) / 1000.0)
                    }
                    if (isCountCoin.value) {
                        coinViewModel.updateCoinBalance(
                            -coinsCount.roundToLong(),
                            CommApplication.Companion.appContext.getString(R.string.query_text_with_gpt)
                        )
                    }

                    if (isResendMessage == true) {
                        updateChatData(
                            idHistory,
                            timestamp,
                            chats.prompt,
                            false,
                            null,
                            botName
                        )
                    } else {
                        saveChatData(
                            idHistory,
                            timestamp,
                            chats.prompt,
                            false,
                            null,
                            botName
                        )
                    }
                }
                .collect { chatResponse ->
                    if (shouldStopCollecting) {
                        gatewayApi.stopCollecting()
                        return@collect
                    }
                    if (chatResponse != null) {
                        // Thêm từng từ vào prompts
                        val newPrompts = chatResponse.chat.prompt.split("") // Tách từng từ
                        newPrompts.forEach { word ->
                            prompts += word // Cập nhật prompts với từng từ
                            inputTokenCount = chatResponse.inputTokenCount
                            outputTokenCount = chatResponse.outputTokenCount

                            // Cập nhật chats với prompts mới
                            chats = chats.copy(
                                prompt = prompts,
                                isError = chatResponse.chat.isError,
                            )

                            // Cập nhật chatState
                            _chatState.update {
                                it.copy(
                                    chatList = it.chatList.toMutableList().apply {
                                        if (isNotEmpty()) set(0, chats)
                                    }
                                )
                            }
                            delay(10)
                        }
                    }
                }
        }
    }

    private fun getResponseWithImageAIGateway(
        context: Context,
        prompt: String,
        bitmap: Bitmap,
        isResendMessage: Boolean? = false
    ) {
        val botName =
            if (PrefAssist.getString(PrefConst.MODEL_AI) == ModelAiMode.GPT.name) Const.AiServiceName.GPT
            else Const.AiServiceName.GEMINI
        val botType =
            if (PrefAssist.getString(PrefConst.MODEL_AI) == ModelAiMode.GPT.name) BotType.BOT_GPT
            else BotType.BOT_GEMINI

        FirebaseAssist.Companion.instance.logCustomEvent(
            "query_ai_response",
            Bundle().apply {
                putString("modal_type", "image")
                putString("question_mode", modeFromScan.value.id.lowercase())
                putString("ai_type", botName)
                putString("local_hour", LocalTime.now().hourOfDay.toString() + "h")
            }
        )
        viewModelScope.launch {
            val timestamp = System.currentTimeMillis()
            val byteArrayBitmap = BitmapUtils.getByteArrayFromBitmap(bitmap)
            if (insertHistory.value) {
                if (idHistory == "") {
                    idHistory = AppDatabase.Companion.getInstance().getHistoryRepository()
                        .insertHistory(
                            history = HistoryEntity(
                                timestamp = timestamp,
                                historyName = "History $timestamp",
                                isFavorite = false,
                                content = prompt,
                                imageData = byteArrayBitmap,
                                questionMode = modeFromScan.value.id,
                                modelAiChat = PrefAssist.getString(PrefConst.MODEL_AI)
                            )
                        )
                    updateHistoryTitle(prompt, bitmap, idHistory)
                }
                insertHistory.value = false
            }

            if (isResendMessage == false) {
                saveChatData(
                    idHistory,
                    timestamp,
                    prompt,
                    true,
                    byteArrayBitmap,
                    botName
                )
            }

            val uid = UUID.randomUUID().toString()

            var chats = Chat(
                idChat = uid,
                prompt = "",
                bitmap = bitmap,
                isFromUser = false,
                isError = false,
                botType = botType
            )
            var prompts = ""

            _chatState.update {
                it.copy(
                    chatList = it.chatList.toMutableList().apply {
                        add(0, chats)
                    }
                )
            }

            var inputTokenCount = 0
            var outputTokenCount = 0
            gatewayApi.getResponseWithImage(context, prompt, bitmap, modeScan.value)
                .onCompletion {
                    val coinsCount: Double = if (PrefAssist.getString(PrefConst.MODEL_AI) != ModelAiMode.GEMINI.name) {
                        inputTokenCount.toDouble() * (RconfAssist.getLong(RconfConst.TOKEN_PROMPT_TO_COIN_GPT) / 1000.0) +
                                outputTokenCount.toDouble() * (RconfAssist.getLong(RconfConst.TOKEN_PROMPT_TO_COIN_GPT) / 1000.0)
                    } else {
                        inputTokenCount.toDouble() * (RconfAssist.getLong(RconfConst.TOKEN_PROMPT_TO_COIN_GEMINI) / 1000.0) +
                                outputTokenCount.toDouble() * (RconfAssist.getLong(RconfConst.TOKEN_ANSWER_TO_COIN_GEMINI) / 1000.0)
                    }
                    if (isCountCoin.value) {
                        coinViewModel.updateCoinBalance(
                            -coinsCount.roundToLong(),
                            CommApplication.Companion.appContext.getString(R.string.query_image_with_gemini)
                        )
                    }

                    if (isResendMessage == true) {
                        updateChatData(
                            idHistory,
                            timestamp,
                            chats.prompt,
                            false,
                            byteArrayBitmap,
                            botName
                        )
                    } else {
                        saveChatData(
                            idHistory,
                            timestamp,
                            chats.prompt,
                            false,
                            byteArrayBitmap,
                            botName
                        )
                    }
                    isLoadingData = false

                }
                .collect { chatResponse ->
                    if (shouldStopCollecting) {
                        gatewayApi.stopCollecting()
                        return@collect
                    }
                    // Thêm từng từ vào prompts
                    val newPrompts = chatResponse.chat.prompt.split("") // Tách từng từ
                    newPrompts.forEach { word ->
                        prompts += word // Cập nhật prompts với từng từ
                        inputTokenCount = chatResponse.inputTokenCount
                        outputTokenCount = chatResponse.outputTokenCount

                        // Cập nhật chats với prompts mới
                        chats = chats.copy(
                            prompt = prompts,
                            isError = chatResponse.chat.isError,
                        )

                        // Cập nhật chatState
                        _chatState.update {
                            it.copy(
                                chatList = it.chatList.toMutableList().apply {
                                    if (isNotEmpty()) set(0, chats)
                                }
                            )
                        }
                        delay(10)
                    }
                }
        }
    }


    private fun updateHistoryTitle(prompt: String, bitmap: Bitmap?, idHistory: String) {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                val title =
                    if (RconfAssist.getBoolean(RconfConst.IS_USE_GATEWAY_API))
                        gatewayApi.getResponsesNoStream(
                            CommApplication.Companion.appContext.getString(
                                R.string.txtid_extract_topic,
                                prompt
                            ),
                            bitmap,
                            modeFromScan.value
                        )
                    else
                        gptApi.getResponsesNoStream(
                            CommApplication.Companion.appContext.getString(
                                R.string.txtid_extract_topic,
                                prompt
                            ),
                            bitmap,
                            modeFromScan.value,
                        )
//                    geminiApi.getResponse(
//                        CommApplication.appContext.getString(R.string.txtid_extract_topic, prompt),
//                        modeFromScan.value,
//                    )
                if (title.chat.prompt.isEmpty()) {
                    return@withContext
                }

//                val inputTokenCount =
//                    PrefAssist.getInt(PrefConst.Token.INPUT_TOKEN_COUNT) + title.inputTokenCount
//                val outputTokenCount =
//                    PrefAssist.getInt(PrefConst.Token.OUTPUT_TOKEN_COUNT) + title.outputTokenCount
//                val totalTokenCountPref =
//                    PrefAssist.getInt(PrefConst.Token.TOTAL_TOKEN_COUNT) + title.totalTokenCount

                val inputTokenCount = title.inputTokenCount
                val outputTokenCount = title.outputTokenCount
                val totalTokenCountPref = title.totalTokenCount

                val historyItem =
                    AppDatabase.Companion.getInstance().getHistoryRepository()
                        .getHistoryById(idHistory)
                historyItem?.let {
                    updateHistory(
                        history = historyItem.copy(historyName = title.chat.prompt.trim())
                    )
                }

                val coinsCount: Double = if (PrefAssist.getString(PrefConst.MODEL_AI) != ModelAiMode.GEMINI.name) {
                    inputTokenCount.toDouble() * (RconfAssist.getLong(RconfConst.TOKEN_PROMPT_TO_COIN_GPT) / 1000.0) +
                            outputTokenCount.toDouble() * (RconfAssist.getLong(RconfConst.TOKEN_PROMPT_TO_COIN_GPT) / 1000.0)
                } else {
                    inputTokenCount.toDouble() * (RconfAssist.getLong(RconfConst.TOKEN_PROMPT_TO_COIN_GEMINI) / 1000.0) +
                            outputTokenCount.toDouble() * (RconfAssist.getLong(RconfConst.TOKEN_ANSWER_TO_COIN_GEMINI) / 1000.0)
                }

                if (isCountCoin.value) {
                    coinViewModel.updateCoinBalance(
                        -coinsCount.roundToLong(),
                        CommApplication.Companion.appContext.getString(R.string.get_title)
                    )
                }
            }
        }
    }

    private fun saveChatData(
        idHistory: String,
        timestamp: Long,
        content: String,
        isHuman: Boolean,
        imageData: ByteArray?,
        botName: String
    ) {
        viewModelScope.launch(Dispatchers.IO) {
            AppDatabase.Companion.getInstance().getChatRepository().insertChat(
                ChatEntity(
                    historyId = idHistory,
                    timestamp = timestamp,
                    content = content,
                    isHuman = isHuman,
                    isError = false,
                    imageData = imageData,
                    botName = botName
                )
            )
            // Update history's modelAiChat to match the latest bot used
            if (!isHuman) {
                val historyDao = AppDatabase.getInstance().getHistoryRepository()
                val history = historyDao.getHistoryById(idHistory)
                history?.let {
                    historyDao.updateHistory(it.copy(modelAiChat = botName))
                }
            }
        }
        debugLogTrace("Save chat data")
    }

    private fun updateChatData(
        idHistory: String,
        timestamp: Long,
        content: String,
        isHuman: Boolean,
        imageData: ByteArray?,
        botName: String
    ) {
        viewModelScope.launch(Dispatchers.IO) {
            val chatDao = AppDatabase.Companion.getInstance().getChatRepository()
            val lastChat = chatDao.getLastChat(idHistory)

            if (lastChat != null) {
                val updatedChat = lastChat.copy(
                    id = lastChat.id,  // Giữ nguyên chatListId
                    timestamp = timestamp,
                    content = content,
                    isHuman = isHuman,
                    imageData = imageData,
                    botName = botName
                )

                chatDao.updateChat(updatedChat)

                // Update history's modelAiChat to match the latest bot used
                if (!isHuman) {
                    val historyDao = AppDatabase.getInstance().getHistoryRepository()
                    val history = historyDao.getHistoryById(idHistory)
                    history?.let {
                        historyDao.updateHistory(it.copy(modelAiChat = botName))
                    }
                }

            } else {
                debugLog("No chat found for historyId: $idHistory")
            }
        }
    }

    // History
    private val _historyList = MutableStateFlow<List<HistoryEntity>>(emptyList())
    val historyList: StateFlow<List<HistoryEntity>> get() = _historyList

    private val _isLoading = MutableStateFlow(true)
    val isLoading: StateFlow<Boolean> = _isLoading


    fun loadHistory() {
        viewModelScope.launch(Dispatchers.IO) {
//            _isLoading.value = true // Bắt đầu trạng thái tải
            try {
                val history =
                    AppDatabase.Companion.getInstance().getHistoryRepository().getAllHistory()
                _historyList.value = history
                delay(150)
            } catch (e: Exception) {
                // Xử lý lỗi nếu cần
            } finally {
                _isLoading.value = false // Hoàn thành trạng thái tải
                isLoadingData = false
            }
        }
    }

    fun deleteHistory(history: HistoryEntity) {
        viewModelScope.launch(Dispatchers.IO) {
            AppDatabase.Companion.getInstance().getChatRepository()
                .deleteChatsForHistory(history.id)
            AppDatabase.Companion.getInstance().getHistoryRepository().deleteHistory(history)
            loadHistory()
        }
    }

    fun deleteSelectedChats(historyIds: List<String>) {
        viewModelScope.launch {
            AppDatabase.Companion.getInstance().getHistoryRepository()
                .deleteHistories(historyIds)
            AppDatabase.Companion.getInstance().getChatRepository()
                .deleteChatsForHistories(historyIds)
            loadHistory()
        }
    }

    fun updateHistory(history: HistoryEntity) {
        viewModelScope.launch(Dispatchers.IO) {
            AppDatabase.Companion.getInstance().getHistoryRepository().updateHistory(history)
            loadHistory()
        }
    }

    //Settings
    fun shareApp(context: Context) {
        val shareIntent = Intent(Intent.ACTION_SEND)
        shareIntent.type = "text/plain"
        shareIntent.putExtra(Intent.EXTRA_SUBJECT, context.getString(R.string.app_name))
        shareIntent.putExtra(
            Intent.EXTRA_TEXT,
            "https://play.google.com/store/apps/details?id=" + context.packageName
        )
        context.startActivity(
            Intent.createChooser(
                shareIntent,
                context.getString(R.string.label_share_app)
            )
        )
    }

    fun openWebPage(context: Context) {
        val intent = Intent(Intent.ACTION_VIEW, Const.POLICY_URL.toUri())
        context.startActivity(intent)
    }
}