package com.amobilab.ezmath.ai.data.db.powerSync

import amobi.module.common.configs.PrefAssist
import amobi.module.common.utils.debugLog
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.powersync.PowerSyncDatabase
import com.powersync.connectors.PowerSyncBackendConnector
import com.powersync.connectors.PowerSyncCredentials
import com.powersync.db.crud.UpdateType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import java.util.concurrent.TimeUnit

/**
 * Data classes cho API responses
 */
@Serializable
data class PowerSyncTokenResponse(
    val powerSyncUrl: String,
    val token: String,
    val userId: String,
    val expiresAt: Long
)

@Serializable
data class ApiDataRequest(
    val table: String,
    val data: JsonElement
)

class MyConnector : PowerSyncBackendConnector() {

    companion object {
        private const val BACKEND_URL = "http://192.168.1.29:3000" // Thay thế bằng URL backend thực
        private const val TIMEOUT_SECONDS = 30L
    }

    private val httpClient = OkHttpClient.Builder()
        .connectTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
        .readTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
        .writeTimeout(TIMEOUT_SECONDS, TimeUnit.SECONDS)
        .build()

    private val json = Json {
        ignoreUnknownKeys = true
        isLenient = true
    }

    /**
     * Chuyển đổi Map<String, String?> thành JsonElement
     */
    private fun mapToJsonElement(data: Map<String, String?>): JsonElement {
        return buildJsonObject {
            data.forEach { (key, value) ->
                if (value != null) {
                    put(key, value)
                } else {
                    put(key, "")
                }
            }
        }
    }
    /**
     * Lấy PowerSync token từ backend
     */
    private suspend fun getPowerSyncToken(): PowerSyncTokenResponse? {
        return withContext(Dispatchers.IO) {
            try {
                val userId = PrefAssist.getString(PrefConst.USER_ID)
                if (userId.isEmpty()) {
                    debugLog("powerSync User ID không tồn tại")
                    return@withContext null
                }

                val request = Request.Builder()
                    .url("$BACKEND_URL/api/auth/token2")
                    .addHeader("User-Id", userId)
                    .addHeader("Content-Type", "application/json")
                    .get()
                    .build()

                val response = httpClient.newCall(request).execute()
                if (response.isSuccessful) {
                    val responseBody = response.body.string()
                    debugLog("powerSync responseBody: $responseBody")
                    responseBody.let {
                        json.decodeFromString<PowerSyncTokenResponse>(it)
                    }
                } else {
                    debugLog("powerSync Lỗi khi lấy PowerSync token: ${response.code}")
                    null
                }
            } catch (e: Exception) {
                debugLog("powerSync Exception khi lấy PowerSync token: ${e.message}")
                null
            }
        }
    }

    override suspend fun fetchCredentials(): PowerSyncCredentials {
        debugLog("powerSync fetchCredentials")

        // Thử lấy token từ backend trước
        val tokenResponse = getPowerSyncToken()

        debugLog("powerSync tokenResponse: $tokenResponse")
        return if (tokenResponse != null) {
            debugLog("powerSync Sử dụng token từ backend")
            PowerSyncCredentials(
                endpoint = tokenResponse.powerSyncUrl,
                token = tokenResponse.token,
                userId = tokenResponse.userId,
            ).also {
                debugLog("powerSync PowerSyncCredentials: $it")
            }

        } else {
            debugLog("powerSync Fallback về demo token")
            // Fallback về demo endpoint nếu không lấy được token từ backend
            PowerSyncCredentials(
                endpoint = "https://684003704e42b293ad972938.powersync.journeyapps.com",
                token = "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
            )
        }
    }

    /**
     * Upsert dữ liệu (tạo mới hoặc cập nhật toàn bộ)
     */
    private suspend fun upsertData(table: String, data: Map<String, String?>): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                debugLog("powerSync upsertData table: $table, data: $data")
                val requestData = ApiDataRequest(table = table, data = mapToJsonElement(data))
                val requestBody = json.encodeToString(ApiDataRequest.serializer(), requestData)
                    .toRequestBody("application/json".toMediaType())

                debugLog("powerSync upsertData requestBody: $requestBody")
                val request = Request.Builder()
                    .url("$BACKEND_URL/api/data")
                    .put(requestBody)
                    .addHeader("Content-Type", "application/json")
                    .build()

                val response = httpClient.newCall(request).execute()
                val success = response.isSuccessful
                if (!success) {
                    debugLog("powerSync Lỗi upsert data: ${response.code} - ${response.message}")
                }
                success
            } catch (e: Exception) {
                debugLog("powerSync Exception upsert data: ${e.message}")
                false
            }
        }
    }

    /**
     * Cập nhật dữ liệu (chỉ các trường được chỉ định)
     */
    private suspend fun updateData(table: String, data: Map<String, String?>): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val requestData = ApiDataRequest(table = table, data = mapToJsonElement(data))
                val requestBody = json.encodeToString(ApiDataRequest.serializer(), requestData)
                    .toRequestBody("application/json".toMediaType())

                val request = Request.Builder()
                    .url("$BACKEND_URL/api/data")
                    .patch(requestBody)
                    .addHeader("Content-Type", "application/json")
                    .build()

                val response = httpClient.newCall(request).execute()
                val success = response.isSuccessful
                if (!success) {
                    debugLog("powerSync Lỗi update data: ${response.code} - ${response.message}")
                }
                success
            } catch (e: Exception) {
                debugLog("powerSync Exception update data: ${e.message}")
                false
            }
        }
    }

    /**
     * Xóa dữ liệu
     */
    private suspend fun deleteData(table: String, id: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val requestData = ApiDataRequest(table = table, data = mapToJsonElement(mapOf("id" to id)))
                val requestBody = json.encodeToString(ApiDataRequest.serializer(), requestData)
                    .toRequestBody("application/json".toMediaType())

                val request = Request.Builder()
                    .url("$BACKEND_URL/api/data")
                    .delete(requestBody)
                    .addHeader("Content-Type", "application/json")
                    .build()

                val response = httpClient.newCall(request).execute()
                val success = response.isSuccessful
                if (!success) {
                    debugLog("powerSync Lỗi delete data: ${response.code} - ${response.message}")
                }
                success
            } catch (e: Exception) {
                debugLog("powerSync Exception delete data: ${e.message}")
                false
            }
        }
    }

    override suspend fun uploadData(database: PowerSyncDatabase) {
        debugLog("powerSync uploadData")
        val batch = database.getCrudBatch() ?: return

        var allSuccess = true

        for (change in batch.crud) {
            val table = change.table
            val id = change.id
            // Đảm bảo data luôn có id
            val data = (change.opData ?: emptyMap()).toMutableMap().apply {
                put("id", id)
                put("user_id", PrefAssist.getString(PrefConst.USER_ID))
            }

            val success = when(change.op) {
                UpdateType.PUT -> {
                    debugLog("powerSync uploadData PUT cho table: $table, id: $id")
                    upsertData(table, data)
                }
                UpdateType.PATCH -> {
                    debugLog("powerSync uploadData PATCH cho table: $table, id: $id")
                    updateData(table, data)
                }
                UpdateType.DELETE -> {
                    debugLog("powerSync uploadData DELETE cho table: $table, id: $id")
                    deleteData(table, id)
                }
            }

            if (success) {
                debugLog("powerSync Thành công ${change.op} cho $table:$id")
            } else {
                debugLog("powerSync Thất bại ${change.op} cho $table:$id")
                allSuccess = false
                // Có thể throw exception hoặc handle error tùy theo yêu cầu
            }
        }

        // Đánh dấu batch đã được xử lý
        // PowerSync sẽ tự động đánh dấu batch là complete sau khi uploadData hoàn thành
        if (allSuccess) {
            debugLog("powerSync Batch hoàn thành thành công")
        } else {
            debugLog("powerSync Batch hoàn thành với một số lỗi")
            // Có thể throw exception để PowerSync retry batch này
            // throw Exception("Một số operations trong batch thất bại")
        }
    }

    /**
     * Đóng HTTP client khi không cần thiết nữa
     */
    fun close() {
        httpClient.dispatcher.executorService.shutdown()
        httpClient.connectionPool.evictAll()
    }
}