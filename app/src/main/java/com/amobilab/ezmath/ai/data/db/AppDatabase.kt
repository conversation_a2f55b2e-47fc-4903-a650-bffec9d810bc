package com.amobilab.ezmath.ai.data.db

import android.content.Context
import com.amobilab.ezmath.ai.data.db.powerSync.AppSchema
import com.powersync.PowerSyncDatabase
import com.powersync.db.schema.Column
import com.powersync.db.schema.Index
import com.powersync.db.schema.IndexedColumn
import com.powersync.db.schema.Schema
import com.powersync.db.schema.Table
import com.powersync.db.schema.ColumnType
import com.amobilab.ezmath.ai.data.db.powerSync.ChatRepository
import com.amobilab.ezmath.ai.data.db.powerSync.CoinHistoryRepository
import com.amobilab.ezmath.ai.data.db.powerSync.HistoryRepository
import com.amobilab.ezmath.ai.data.db.powerSync.MyConnector
import com.powersync.DatabaseDriverFactory
import java.io.File

class AppDatabase private constructor() {

    companion object {
        @JvmStatic
        @Synchronized
        fun getInstance() = Holder.instance

        const val DB_NAME = "app_db"


    }

    private object Holder {
        val instance = AppDatabase()
    }

    private lateinit var db: PowerSyncDatabase
    private lateinit var historyRepository: HistoryRepository
    private lateinit var chatRepository: ChatRepository
    private lateinit var coinHistoryRepository: CoinHistoryRepository

    fun init(context: Context) {
        if (!::db.isInitialized) {
            // Khởi tạo PowerSyncDatabase
            val driverFactory = DatabaseDriverFactory(context)
            val dbPath = File(context.filesDir, DB_NAME)
            db = PowerSyncDatabase(
                factory = driverFactory,
                schema = AppSchema,
                dbFilename = dbPath.absolutePath
            )

            // Khởi tạo các repository
            historyRepository = HistoryRepository(db)
            chatRepository = ChatRepository(db)
            coinHistoryRepository = CoinHistoryRepository(db)
        }
    }

    fun getHistoryRepository(): HistoryRepository {
        check(::historyRepository.isInitialized) { "Database not initialized. Call init() first." }
        return historyRepository
    }

    fun getChatRepository(): ChatRepository {
        check(::chatRepository.isInitialized) { "Database not initialized. Call init() first." }
        return chatRepository
    }

    fun getCoinHistoryRepository(): CoinHistoryRepository {
        check(::coinHistoryRepository.isInitialized) { "Database not initialized. Call init() first." }
        return coinHistoryRepository
    }

    // Phương thức để kết nối với backend
    suspend fun connectToBackend(/* backend configuration */) {
        // TODO: Cấu hình đồng bộ với backend (ví dụ: Supabase, custom server)
         db.connect(MyConnector())
    }
}